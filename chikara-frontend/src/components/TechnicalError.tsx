import errorIcon from "@/assets/icons/UI/NetworkError.png";
import { cn } from "@/lib/utils";
import { Link } from "react-router-dom";
import Spinner from "./Spinners/Spinner";

function TechnicalError({ error, boundary }: { error?: Error; boundary?: boolean }) {
    const isServerOffline = error?.message === "Server Offline";

    if (
        boundary &&
        (error.message.includes("dynamically imported module") ||
            error.message.includes("Importing a module script failed"))
    ) {
        setTimeout(() => {
            window.location.reload();
        }, 1000);
        return <Spinner center />;
    }

    return (
        <div
            className={cn(
                boundary ? "min-h-full px-4 py-8 sm:px-6 sm:py-12 md:place-items-center md:pt-16 lg:px-8" : "mx-2 my-4",
                "max-w-2xl mx-auto text-stroke-sm"
            )}
        >
            <img
                src={errorIcon}
                alt=""
                className={cn(boundary ? "mb-4 w-48 md:mb-6" : "mb-3 w-32", "mx-auto h-auto")}
            />
            <div className="mx-auto max-w-lg text-center">
                <div className="space-y-4">
                    <p className="font-extrabold text-4xl text-indigo-600 sm:text-5xl">Uh Oh!</p>
                    <div>
                        <h1 className="text-3xl text-gray-200 sm:text-4xl font-semibold">
                            {isServerOffline ? "Server Offline" : "Technical Error"}
                        </h1>
                        <p className="mt-2 text-sm text-gray-400">
                            {error?.message && !isServerOffline ? error.message : "Please try again later."}
                        </p>
                    </div>
                    <div className="flex justify-center">
                        {boundary ? (
                            <a
                                href="/"
                                className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                Retry
                            </a>
                        ) : (
                            <Link
                                to={"-1"}
                                className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                            >
                                Back
                            </Link>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default TechnicalError;
