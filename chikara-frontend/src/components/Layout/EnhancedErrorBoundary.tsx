import React, { ReactNode, ErrorInfo } from "react";
import ErrorBoundary from "./ErrorBoundary";

interface EnhancedErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    showDetails?: boolean;
    enableRetry?: boolean;
    enableReporting?: boolean;
    level?: "page" | "component" | "critical";
}

/**
 * Enhanced Error Boundary wrapper that provides additional configuration options
 * while delegating the actual error boundary logic to the ErrorBoundary component
 */
function EnhancedErrorBoundary({
    children,
    fallback,
    onError,
    showDetails = false,
    enableRetry = true,
    enableReporting = false,
    level = "component",
}: EnhancedErrorBoundaryProps) {
    return (
        <ErrorBoundary
            fallback={fallback}
            showDetails={showDetails}
            enableRetry={enableRetry}
            enableReporting={enableReporting}
            level={level}
            onError={onError}
        >
            {children}
        </ErrorBoundary>
    );
}

export default EnhancedErrorBoundary;
