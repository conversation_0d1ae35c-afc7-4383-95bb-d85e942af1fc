import React, { Component, ErrorInfo, ReactNode } from "react";
import { RefreshCw, Home } from "lucide-react";
import { cn } from "@/lib/utils";
import errorIcon from "@/assets/icons/UI/NetworkError.png";

interface ErrorBoundaryProps {
    children: ReactNode;
    fallback?: ReactNode;
    onError?: (error: Error, errorInfo: ErrorInfo) => void;
    showDetails?: boolean;
    enableRetry?: boolean;
    enableReporting?: boolean;
    level?: "page" | "component" | "critical";
}

interface ErrorBoundaryState {
    hasError: boolean;
    error?: Error;
    errorInfo?: ErrorInfo;
    retryCount: number;
    errorId: string;
}

interface ErrorCategory {
    type: "chunk" | "network" | "runtime" | "unknown";
    severity: "low" | "medium" | "high" | "critical";
    recoverable: boolean;
    autoRetry: boolean;
}

/**
 * Categorizes errors for better handling and user experience
 */
function categorizeError(error: Error): ErrorCategory {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || "";

    // Chunk loading errors (usually recoverable with refresh)
    if (
        message.includes("loading chunk") ||
        message.includes("dynamically imported module") ||
        message.includes("importing a module script failed")
    ) {
        return {
            type: "chunk",
            severity: "medium",
            recoverable: true,
            autoRetry: true,
        };
    }

    // Network errors
    if (message.includes("network") || message.includes("fetch") || message.includes("timeout")) {
        return {
            type: "network",
            severity: "medium",
            recoverable: true,
            autoRetry: false,
        };
    }

    // Runtime errors
    if (
        message.includes("cannot read property") ||
        message.includes("undefined is not a function") ||
        stack.includes("typeerror")
    ) {
        return {
            type: "runtime",
            severity: "high",
            recoverable: false,
            autoRetry: false,
        };
    }

    return {
        type: "unknown",
        severity: "high",
        recoverable: false,
        autoRetry: false,
    };
}

/**
 * Generates a unique error ID for tracking and reporting
 */
function generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Enhanced Error Boundary with better error categorization, retry functionality,
 * and user-friendly error displays
 */
class EnhancedErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
    private retryTimeoutId: NodeJS.Timeout | null = null;

    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = {
            hasError: false,
            retryCount: 0,
            errorId: "",
        };
    }

    static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
        return {
            hasError: true,
            error,
            errorId: generateErrorId(),
        };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
        this.setState({ errorInfo });

        // Log error
        console.error("Error caught by Enhanced Error Boundary:", {
            error,
            errorInfo,
            errorId: this.state.errorId,
        });

        // Call custom error handler
        this.props.onError?.(error, errorInfo);

        // Report error if enabled
        if (this.props.enableReporting) {
            this.reportError(error, errorInfo);
        }

        // Auto-retry for certain error types
        const category = categorizeError(error);
        if (category.autoRetry && this.state.retryCount < 3) {
            this.scheduleRetry(category.type === "chunk" ? 1000 : 3000);
        }
    }

    componentWillUnmount(): void {
        if (this.retryTimeoutId) {
            clearTimeout(this.retryTimeoutId);
        }
    }

    private scheduleRetry = (delay: number): void => {
        this.retryTimeoutId = setTimeout(() => {
            this.handleRetry();
        }, delay);
    };

    private handleRetry = (): void => {
        this.setState((prevState) => ({
            hasError: false,
            error: undefined,
            errorInfo: undefined,
            retryCount: prevState.retryCount + 1,
        }));
    };

    private handleRefresh = (): void => {
        window.location.reload();
    };

    private handleGoHome = (): void => {
        window.location.href = "/";
    };

    private reportError = (error: Error, errorInfo: ErrorInfo): void => {
        // Here you would integrate with your error reporting service
        // For example: Sentry, LogRocket, etc.
        console.log("Reporting error:", {
            error: error.message,
            stack: error.stack,
            componentStack: errorInfo.componentStack,
            errorId: this.state.errorId,
        });
    };

    private renderErrorUI(): ReactNode {
        const { error } = this.state;
        const { showDetails = false, enableRetry = true, level = "component" } = this.props;

        if (!error) return null;

        const category = categorizeError(error);
        const isChunkError = category.type === "chunk";
        const isCritical = level === "critical" || category.severity === "critical";

        // Auto-refresh for chunk errors
        if (isChunkError && this.state.retryCount === 0) {
            setTimeout(() => this.handleRefresh(), 1000);
            return (
                <div className="flex items-center justify-center min-h-[200px] p-8">
                    <div className="text-center">
                        <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
                        <p className="text-gray-600 dark:text-gray-400">Updating application...</p>
                    </div>
                </div>
            );
        }

        return (
            <div
                className={cn(
                    isCritical
                        ? "min-h-full px-4 py-8 sm:px-6 sm:py-12 md:place-items-center md:pt-16 lg:px-8"
                        : "mx-2 my-4",
                    "max-w-2xl mx-auto text-stroke-sm"
                )}
            >
                <img
                    src={errorIcon}
                    alt=""
                    className={cn(isCritical ? "mb-4 w-48 md:mb-6" : "mb-3 w-32", "mx-auto h-auto")}
                />
                <div className="mx-auto max-w-lg text-center">
                    <div className="space-y-4">
                        <p className="font-extrabold text-4xl text-indigo-600 sm:text-5xl">Uh Oh!</p>
                        <div>
                            <h1 className="text-3xl text-gray-200 sm:text-4xl font-semibold">
                                {this.getErrorTitle(category)}
                            </h1>
                            <p className="mt-2 text-sm text-gray-400">{this.getErrorMessage(category)}</p>
                        </div>

                        <div className="flex justify-center">
                            {enableRetry && category.recoverable ? (
                                <button
                                    className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    onClick={this.handleRetry}
                                >
                                    <RefreshCw className="w-4 h-4 mr-2" />
                                    Try Again
                                </button>
                            ) : isCritical ? (
                                <a
                                    href="/"
                                    className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                >
                                    <Home className="w-4 h-4 mr-2" />
                                    Go Home
                                </a>
                            ) : (
                                <button
                                    className="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    onClick={this.handleRefresh}
                                >
                                    <RefreshCw className="w-4 h-4 mr-2" />
                                    Refresh
                                </button>
                            )}
                        </div>

                        {/* Error Details */}
                        {showDetails && (
                            <details className="text-left bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                                <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Technical Details
                                </summary>
                                <div className="text-xs text-gray-600 dark:text-gray-400 space-y-2">
                                    <div>
                                        <strong>Error:</strong> {error.message}
                                    </div>
                                    <div>
                                        <strong>Error ID:</strong> {this.state.errorId}
                                    </div>
                                    {error.stack && (
                                        <div>
                                            <strong>Stack:</strong>
                                            <pre className="mt-1 text-xs overflow-auto max-h-32 bg-gray-200 dark:bg-gray-700 p-2 rounded">
                                                {error.stack}
                                            </pre>
                                        </div>
                                    )}
                                </div>
                            </details>
                        )}

                        {/* Retry Count */}
                        {this.state.retryCount > 0 && (
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                Retry attempts: {this.state.retryCount}
                            </p>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    private getErrorTitle(category: ErrorCategory): string {
        switch (category.type) {
            case "chunk":
                return "Application Update Required";
            case "network":
                return "Connection Problem";
            case "runtime":
                return "Something Went Wrong";
            default:
                return "Unexpected Error";
        }
    }

    private getErrorMessage(category: ErrorCategory): string {
        switch (category.type) {
            case "chunk":
                return "The application has been updated. Please refresh the page to continue.";
            case "network":
                return "Unable to connect to the server. Please check your internet connection and try again.";
            case "runtime":
                return "An unexpected error occurred. Our team has been notified and is working on a fix.";
            default:
                return "Something unexpected happened. Please try refreshing the page or contact support if the problem persists.";
        }
    }

    render(): ReactNode {
        if (this.state.hasError) {
            return this.props.fallback || this.renderErrorUI();
        }

        return this.props.children;
    }
}

export default EnhancedErrorBoundary;
