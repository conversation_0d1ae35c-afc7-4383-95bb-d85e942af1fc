import App from "@/App";
import AuthLayout from "@/components/Layout/AuthLayout";
import Layout from "@/components/Layout/Layout";
import ErrorPage from "@/pages/ErrorPage";
import AuthRedirect from "@/features/auth/components/AuthRedirect";
import ForgotPassword from "@/features/auth/components/ForgotPassword";
import Login from "@/features/auth/components/Login";
import PasswordReset from "@/features/auth/components/PasswordReset";
import Register from "@/features/auth/components/Register";
import NotFoundPage from "@/pages/NotFoundPage";
import TestPage from "@/pages/TestPage";
import { Navigate, RouterProvider, createBrowserRouter, redirect } from "react-router-dom";
import ProtectedAuth from "../Auth/ProtectedAuth";
import { useAuthStore } from "../store/stores";
import protectedRoutes from "./protectedRoutes";

const RootRedirect = () => {
    const authed = isAuthenticated();
    return authed ? <Navigate replace to="/home" /> : <Navigate replace to="/login" />;
};

const isAuthenticated = () => {
    const { authed } = useAuthStore.getState();
    return !!authed;
};

const noAuthRedirectLoader = async () => {
    const isAuth = isAuthenticated();
    if (!isAuth) {
        return redirect("/login");
    }
    return null;
};

const isAuthedRedirectLoader = async () => {
    const isAuth = isAuthenticated();
    if (isAuth) {
        return redirect("/");
    }
    return null;
};

const router = createBrowserRouter([
    {
        path: "/",
        Component: App,
        errorElement: <ErrorPage />,
        children: [
            {
                index: true,
                Component: RootRedirect,
            },
            // Public Auth Routes
            {
                Component: AuthLayout,
                loader: isAuthedRedirectLoader,
                children: [
                    {
                        path: "callback",
                        Component: AuthRedirect,
                    },
                    {
                        path: "login",
                        Component: Login,
                    },
                    {
                        path: "register",
                        Component: Register,
                    },
                    {
                        path: "register/:registerCode",
                        Component: Register,
                    },
                    {
                        path: "forgotpassword",
                        Component: ForgotPassword,
                    },
                    {
                        path: "passwordReset",
                        Component: PasswordReset,
                    },
                ],
            },
            // Protected Routes with layout
            {
                Component: Layout,
                loader: noAuthRedirectLoader,
                children: [
                    {
                        path: "*",
                        Component: ProtectedAuth,
                        children: protectedRoutes,
                    },
                ],
            },
            // Protected Routes without layout
            {
                Component: ProtectedAuth,
                loader: noAuthRedirectLoader,
                children: [
                    {
                        path: "/testpage",
                        Component: TestPage,
                    },
                ],
            },
            // Not Found
            {
                path: "*",
                Component: NotFoundPage,
            },
        ],
    },
]);

export const AppRouter = () => {
    return <RouterProvider router={router} />;
};

export default AppRouter;
