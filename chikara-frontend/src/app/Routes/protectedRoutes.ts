import { lazy } from "react";

import Battle from "@/pages/BattlePage";
import ErrorPage from "@/pages/ErrorPage";
import ConstructionPage from "@/pages/ConstructionPage";
import Home from "@/pages/Home";
import NotFoundPage from "@/pages/NotFoundPage";
import Profile from "@/pages/Profile";
import Social from "@/pages/Social";
import ConstructionRedirect from "../Auth/ConstructionRedirect";

const Inbox = lazy(() => import("@/pages/Inbox"));
const Bank = lazy(() => import("@/pages/Bank"));
const ShoeLocker = lazy(() => import("@/pages/ShoeLocker"));
const Training = lazy(() => import("@/pages/Training"));
const Hospital = lazy(() => import("@/pages/Hospital"));
const Jail = lazy(() => import("@/pages/Jail"));
const Leaderboard = lazy(() => import("@/pages/Leaderboard"));
const Shops = lazy(() => import("@/pages/Shops"));
const SingleShop = lazy(() => import("@/pages/SingleShop"));
const Inventory = lazy(() => import("@/pages/Inventory"));
const FacultyList = lazy(() => import("@/pages/FacultyList"));
const Settings = lazy(() => import("@/pages/Settings"));
const Events = lazy(() => import("@/pages/Events"));
const School = lazy(() => import("@/pages/School"));
const CraftingWorkshop = lazy(() => import("@/pages/CraftingWorkshopPage"));
const Courses = lazy(() => import("@/pages/Courses"));
const FullChat = lazy(() => import("@/pages/FullChat"));
const PartTimeJobListings = lazy(() => import("@/pages/PartTimeJobListings"));
const YourJob = lazy(() => import("@/pages/YourJob"));
const Updates = lazy(() => import("@/pages/Updates"));
const Adventure = lazy(() => import("@/pages/AdventurePage"));
const Streets = lazy(() => import("@/pages/StreetsPage"));
const TalentsView = lazy(() => import("@/pages/TalentsView"));
const TalentTree = lazy(() => import("@/pages/TalentTree"));
const Casino = lazy(() => import("@/pages/Casino"));
const Arcade = lazy(() => import("../../features/arcade/Arcade"));
const Abilities = lazy(() => import("@/pages/Abilities"));
const Discord = lazy(() => import("@/pages/Discord"));
const Tasks = lazy(() => import("@/pages/TasksPage"));
const BountyBoard = lazy(() => import("@/pages/BountyBoard"));
const Suggestions = lazy(() => import("@/pages/Suggestions"));
const Missions = lazy(() => import("@/pages/Missions"));
// const Classroom = lazy(() => import("@/pages/Classroom"));
const Shrine = lazy(() => import("@/pages/Shrine"));
// const GameStats = lazy(() => import("@/pages/GameStats"));
const Referrals = lazy(() => import("@/pages/Referrals"));
const Polls = lazy(() => import("@/pages/Polls"));
const Dailies = lazy(() => import("@/pages/DailyTaskPage"));
const Gang = lazy(() => import("@/pages/Gang"));
const GangList = lazy(() => import("@/pages/GangList"));
const GangLeaderboards = lazy(() => import("@/pages/GangLeaderboards"));
const LatestNews = lazy(() => import("@/pages/LatestNews"));
const Market = lazy(() => import("@/pages/Market"));
const Rooftop = lazy(() => import("@/pages/Rooftop"));
const Character = lazy(() => import("@/pages/Character"));
const Pets = lazy(() => import("@/pages/PetsPage"));
const ExplorePage = lazy(() => import("@/pages/ExplorePage"));
const PropertyPage = lazy(() => import("@/pages/PropertyPage"));

const protectedRoutes = [
    { path: "*", Component: NotFoundPage },
    { path: "home", Component: Home },
    { path: "explore", Component: ExplorePage },
    { path: "campus", Component: School },
    { path: "inventory", Component: Inventory },
    { path: "facultylist", Component: FacultyList },
    { path: "character", Component: Character },
    { path: "settings", Component: Settings },
    { path: "events", Component: Events },
    { path: "bank", Component: Bank },
    { path: "training", Component: Training },
    { path: "fight", Component: Battle },
    { path: "profile/:id", Component: Profile },
    { path: "shops", Component: Shops },
    { path: "hospital", Component: Hospital },
    { path: "jail", Component: Jail },
    { path: "leaderboard", Component: Leaderboard },
    { path: "workshop", Component: CraftingWorkshop },
    { path: "job", Component: YourJob },
    { path: "joblistings", Component: PartTimeJobListings },
    { path: "social", Component: Social },
    {
        path: "shops",
        children: [{ path: ":shopID", Component: SingleShop }],
    },
    {
        path: "talents",
        Component: TalentsView,
        children: [
            { path: "strength", Component: TalentTree },
            { path: "defence", Component: TalentTree },
            { path: "intelligence", Component: TalentTree },
            { path: "dexterity", Component: TalentTree },
            { path: "endurance", Component: TalentTree },
            { path: "vitality", Component: TalentTree },
        ],
    },
    { path: "chat", Component: FullChat },
    { path: "error", Component: ErrorPage },
    { path: "updates", Component: Updates },
    { path: "adventure", Component: Adventure },
    { path: "streets", Component: Streets },
    { path: "abilities", Component: Abilities },
    { path: "equipment", Component: Inventory },
    { path: "arcade", Component: Arcade },
    { path: "tasks", Component: Tasks },
    { path: "courses", Component: Courses },
    { path: "casino", Component: Casino },
    { path: "bountyboard", Component: BountyBoard },
    { path: "suggestions", Component: Suggestions },
    { path: "discord", Component: Discord },
    { path: "missions", Component: Missions },
    // { path: "classroom", Component: Classroom },
    { path: "shrine", Component: Shrine },
    // { path: "stats", Component: <GameStats /> },
    { path: "refer", Component: Referrals },
    { path: "polls", Component: Polls },
    { path: "dailies", Component: Dailies },
    { path: "market", Component: Market },
    { path: "rooftop", Component: Rooftop },
    { path: "gang", Component: Gang },
    { path: "ganglist", Component: GangList },
    { path: "gang/leaderboards", Component: GangLeaderboards },
    { path: "news", Component: LatestNews },
    { path: "news/:postID", Component: LatestNews },
    { path: "inbox", Component: Inbox },
    { path: "inbox/:id", Component: Inbox },
    { path: "pets", Component: Pets },
    { path: "property", Component: PropertyPage },

    // PAGES UNDER CONSTRUCTION
    {
        Component: ConstructionRedirect,
        children: [
            { path: "housing", Component: ConstructionPage },
            { path: "worldboss", Component: ConstructionPage },
            { path: "springs", Component: ConstructionPage },
            { path: "premium", Component: ConstructionPage },
            { path: "search", Component: ConstructionPage },
            { path: "classroom", Component: ConstructionPage },
            { path: "studentcouncil", Component: ConstructionPage },
            { path: "shoelocker", Component: ShoeLocker },
        ],
    },
];

export default protectedRoutes;
